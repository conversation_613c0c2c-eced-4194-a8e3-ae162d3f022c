import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  generateSlug
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/legal-pages - Get all legal pages with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'slug', 'content', 'metadescription'])

  // Build sort query with field name mapping
  const fieldMapping: { [key: string]: string } = {
    'updatedAt': 'updatedat',
    'createdAt': 'createdat',
    'lastModified': 'lastmodified',
    'isActive': 'isactive',
    'displayOrder': 'displayorder',
    'metaDescription': 'metadescription'
  }

  const mappedSortBy = fieldMapping[sortBy] || sortBy
  const sortQuery = buildSortQuery(mappedSortBy, sortOrder)

  // Get legal pages with pagination
  const [legalPages, total] = await Promise.all([
    prisma.legalpages.findMany({
      where: searchQuery,
      include: {
        legalpagesections: {
          orderBy: { displayorder: 'asc' }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.legalpages.count({ where: searchQuery })
  ])

  // Transform legal pages to frontend format
  const transformedLegalPages = legalPages.map(page => transformFromDbFields.legalPage(page))

  return paginatedResponse(transformedLegalPages, total, page, limit, 'Legal pages retrieved successfully')
})

// POST /api/admin/legal-pages - Create a new legal page
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas['legal-pages'].create)
  const data = await validate(request)

  // Generate slug if not provided
  if (!data.slug) {
    data.slug = generateSlug(data.title)
  }

  // Check if slug already exists
  const existingPage = await prisma.legalpages.findUnique({
    where: { slug: data.slug },
  })

  if (existingPage) {
    // Generate a unique slug
    let counter = 1
    let newSlug = `${data.slug}-${counter}`

    while (await prisma.legalpages.findUnique({ where: { slug: newSlug } })) {
      counter++
      newSlug = `${data.slug}-${counter}`
    }

    data.slug = newSlug
  }

  // Transform data to database format
  const dbData = transformToDbFields.legalPage(data)

  const legalPage = await prisma.legalpages.create({
    data: dbData,
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  const transformedLegalPage = transformFromDbFields.legalPage(legalPage)
  return successResponse(transformedLegalPage, 'Legal page created successfully', 201)
})

// PUT /api/admin/legal-pages - Bulk update legal pages
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid legal page IDs provided')
  }

  // Transform data to database format
  const dbData = transformToDbFields.legalPage(data)

  const updatedLegalPages = await prisma.legalpages.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
  })

  return successResponse(
    { count: updatedLegalPages.count },
    `${updatedLegalPages.count} legal pages updated successfully`
  )
})

// DELETE /api/admin/legal-pages - Bulk delete legal pages
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid legal page IDs provided')
  }

  // First delete all associated sections
  await prisma.legalpagesSection.deleteMany({
    where: {
      legalpageid: {
        in: ids,
      },
    },
  })

  // Then delete the legal pages
  const deletedLegalPages = await prisma.legalpages.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedLegalPages.count },
    `${deletedLegalPages.count} legal pages deleted successfully`
  )
})
