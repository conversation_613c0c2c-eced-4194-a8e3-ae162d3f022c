'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon,
  DocumentTextIcon,
  LinkIcon,
  InformationCircleIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'

interface LegalPage {
  id: string
  title: string
  slug: string
  content: string
  metaDescription?: string
  isActive: boolean
  displayOrder: number
  lastModified: string
  modifiedBy?: string
  createdAt: string
  updatedAt?: string
}

interface LegalPageModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  legalPage?: LegalPage | null
  config: {
    apiEndpoint: string
    formLayout: {
      type: 'compact' | 'full'
      columns: number
      sections: Array<{
        title: string
        fields: string[]
      }>
    }
  }
}

export function LegalPageModal({ isOpen, onClose, onSave, legalPage, config }: LegalPageModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    content: '',
    metaDescription: '',
    isActive: true,
    displayOrder: 0
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [previewMode, setPreviewMode] = useState(false)

  useEffect(() => {
    if (legalPage) {
      setFormData({
        title: legalPage.title || '',
        slug: legalPage.slug || '',
        content: legalPage.content || '',
        metaDescription: legalPage.metaDescription || '',
        isActive: legalPage.isActive ?? true,
        displayOrder: legalPage.displayOrder || 0
      })
    } else {
      setFormData({
        title: '',
        slug: '',
        content: '',
        metaDescription: '',
        isActive: true,
        displayOrder: 0
      })
    }
    setError(null)
    setPreviewMode(false)
  }, [legalPage, isOpen])

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: prev.slug || generateSlug(title)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const url = legalPage 
        ? `${config.apiEndpoint}/${legalPage.id}`
        : config.apiEndpoint

      const method = legalPage ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to save legal page')
      }

      onSave()
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save legal page')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex min-h-screen items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {legalPage ? 'Edit Legal Page' : 'Create Legal Page'}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {legalPage ? 'Update legal page information' : 'Add a new legal page to your site'}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPreviewMode(!previewMode)}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  {previewMode ? (
                    <>
                      <EyeSlashIcon className="h-4 w-4 mr-2" />
                      Edit
                    </>
                  ) : (
                    <>
                      <EyeIcon className="h-4 w-4 mr-2" />
                      Preview
                    </>
                  )}
                </button>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
              {previewMode ? (
                <div className="p-6">
                  <div className="prose max-w-none">
                    <h1>{formData.title}</h1>
                    {formData.metaDescription && (
                      <p className="text-gray-600 italic">{formData.metaDescription}</p>
                    )}
                    <div dangerouslySetInnerHTML={{ __html: formData.content }} />
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="p-6">
                  {error && (
                    <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                      <div className="flex">
                        <InformationCircleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                          <p className="text-sm text-red-800">{error}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-6">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h4>
                        
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                              Title *
                            </label>
                            <input
                              type="text"
                              id="title"
                              value={formData.title}
                              onChange={(e) => handleTitleChange(e.target.value)}
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              required
                            />
                          </div>

                          <div>
                            <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
                              Slug *
                            </label>
                            <div className="flex">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                /legal/
                              </span>
                              <input
                                type="text"
                                id="slug"
                                value={formData.slug}
                                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                                className="flex-1 block w-full px-3 py-2 border border-gray-300 rounded-none rounded-r-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                required
                              />
                            </div>
                          </div>

                          <div>
                            <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-700 mb-1">
                              Meta Description
                            </label>
                            <textarea
                              id="metaDescription"
                              rows={3}
                              value={formData.metaDescription}
                              onChange={(e) => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Brief description for SEO and search results"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Settings */}
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 mb-4">Settings</h4>
                        
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="displayOrder" className="block text-sm font-medium text-gray-700 mb-1">
                              Display Order
                            </label>
                            <input
                              type="number"
                              id="displayOrder"
                              value={formData.displayOrder}
                              onChange={(e) => setFormData(prev => ({ ...prev, displayOrder: parseInt(e.target.value) || 0 }))}
                              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              min="0"
                            />
                          </div>

                          <div className="flex items-center">
                            <input
                              id="isActive"
                              type="checkbox"
                              checked={formData.isActive}
                              onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                              Active
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="lg:col-span-1">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 mb-4">Content</h4>
                        
                        <div>
                          <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                            Legal Content *
                          </label>
                          <textarea
                            id="content"
                            rows={20}
                            value={formData.content}
                            onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                            placeholder="Enter the legal content here. You can use HTML for formatting."
                            required
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            HTML formatting is supported. Use standard HTML tags for structure and styling.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              )}
            </div>

            {/* Footer */}
            {!previewMode && (
              <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  disabled={loading}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : (legalPage ? 'Update Legal Page' : 'Create Legal Page')}
                </button>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
