'use client'

import { LegalPagesManager } from '@/components/admin/legal-pages/legal-pages-manager'

export default function LegalPagesPage() {
  const config = {
    title: 'Legal Pages',
    description: 'Manage privacy policies, terms of service, and other legal documentation',
    apiEndpoint: '/api/admin/legal-pages',
    permissions: {
      create: true,
      read: true,
      update: true,
      delete: true,
      bulkActions: true,
    },
    fields: [
      {
        key: 'title',
        label: 'Title',
        type: 'text',
        required: true,
        searchable: true,
        sortable: true,
      },
      {
        key: 'slug',
        label: 'Slug',
        type: 'text',
        required: true,
        searchable: true,
        sortable: true,
      },
      {
        key: 'content',
        label: 'Content',
        type: 'textarea',
        required: true,
        searchable: true,
      },
      {
        key: 'metaDescription',
        label: 'Meta Description',
        type: 'textarea',
        searchable: true,
      },
      {
        key: 'isActive',
        label: 'Status',
        type: 'boolean',
        sortable: true,
        filterable: true,
      },
      {
        key: 'displayOrder',
        label: 'Display Order',
        type: 'number',
        sortable: true,
      },
      {
        key: 'lastModified',
        label: 'Last Modified',
        type: 'date',
        sortable: true,
      },
      {
        key: 'actions',
        label: 'Actions',
        type: 'actions',
      },
    ],
    actions: [
      {
        key: 'view',
        label: 'View',
        icon: 'eye',
        variant: 'secondary' as const,
      },
      {
        key: 'edit',
        label: 'Edit',
        icon: 'pencil',
        variant: 'primary' as const,
        permission: 'update',
      },
      {
        key: 'toggle',
        label: 'Toggle Status',
        icon: 'check-circle',
        variant: 'secondary' as const,
        permission: 'update',
      },
      {
        key: 'delete',
        label: 'Delete',
        icon: 'trash',
        variant: 'danger' as const,
        permission: 'delete',
      },
    ],
    bulkActions: [
      {
        key: 'activate',
        label: 'Activate',
        icon: 'check-circle',
        variant: 'primary' as const,
        permission: 'update',
      },
      {
        key: 'deactivate',
        label: 'Deactivate',
        icon: 'x-circle',
        variant: 'secondary' as const,
        permission: 'update',
      },
      {
        key: 'delete',
        label: 'Delete',
        icon: 'trash',
        variant: 'danger' as const,
        permission: 'delete',
      },
    ],
    formLayout: {
      type: 'compact' as const,
      columns: 2,
      sections: [
        {
          title: 'Basic Information',
          fields: ['title', 'slug', 'metaDescription'],
        },
        {
          title: 'Content',
          fields: ['content'],
        },
        {
          title: 'Settings',
          fields: ['isActive', 'displayOrder'],
        },
      ],
    },
  }

  return <LegalPagesManager config={config} />

