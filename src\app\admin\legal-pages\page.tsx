'use client';

import { TeamMembersManager } from '@/components/admin/team-members/team-members-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface LegalPage {
  id: number
  title: string
  slug: string
  content: string
  metaDescription?: string
  isActive: boolean
  displayOrder: number
  lastModified: string
  modifiedBy?: string
  createdAt: string
  updatedAt: string
}

const legalPageConfig: CrudConfig<LegalPage> = {
  title: 'Legal Pages',
  description: 'Manage privacy policies, terms of service, and other legal documentation',
  endpoint: 'legal-pages', // API endpoint

  columns: [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '200px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'slug',
      label: 'Slug',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'metaDescription',
      label: 'Description',
      sortable: false,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'displayOrder',
      label: 'Order',
      sortable: true,
      renderType: 'number',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'lastModified',
      label: 'Last Modified',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'updatedAt',
      label: 'Last Active',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      },
      hideable: true,
      defaultVisible: true
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View legal page'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit legal page'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Status',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Activate/Deactivate legal page'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete legal page'
    }
  ],

  fields: [
    {
      key: 'title',
      label: 'Title',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'Enter legal page title'
    },
    {
      key: 'slug',
      label: 'Slug',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., privacy-policy'
    },
    {
      key: 'metaDescription',
      label: 'Meta Description',
      type: 'textarea',
      searchable: true,
      placeholder: 'Brief description for SEO and search results',
      rows: 3
    },
    {
      key: 'content',
      label: 'Content',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Enter the legal content here. HTML formatting is supported.',
      rows: 10
    },
    {
      key: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      searchable: false,
      placeholder: '0'
    },
    {
      key: 'isActive',
      label: 'Active Status',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
  ],

  filters: [
    {
      key: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search legal pages by title, slug, content...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['title', 'slug', 'metaDescription', 'lastModified', 'updatedAt', 'isActive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Basic Information',
        fields: ['title', 'slug', 'metaDescription']
      },
      {
        title: 'Content',
        fields: ['content']
      },
      {
        title: 'Settings',
        fields: ['displayOrder', 'isActive']
      }
    ]
  }
};

export default function LegalPagesPage() {
  return <TeamMembersManager config={legalPageConfig} />;
}
