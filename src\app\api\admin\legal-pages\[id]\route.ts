import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError,
  generateSlug
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/legal-pages/[id] - Get a specific legal page
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const legalPage = await prisma.legalpages.findUnique({
    where: { id },
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  if (!legalPage) {
    throw new ApiError('Legal page not found', 404)
  }

  const transformedLegalPage = transformFromDbFields.legalPage(legalPage)
  return successResponse(transformedLegalPage)
})

// PUT /api/admin/legal-pages/[id] - Update a legal page
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas['legal-pages'].update)
  const data = await validate(request)

  // Check if legal page exists
  const existingPage = await prisma.legalpages.findUnique({
    where: { id },
  })

  if (!existingPage) {
    throw new ApiError('Legal page not found', 404)
  }

  // Generate slug if title is updated but slug is not provided
  if (data.title && !data.slug) {
    data.slug = generateSlug(data.title)
  }

  // If slug is being updated, check for conflicts
  if (data.slug && data.slug !== existingPage.slug) {
    const slugConflict = await prisma.legalpages.findFirst({
      where: {
        slug: data.slug,
        id: { not: id },
      },
    })

    if (slugConflict) {
      // Generate a unique slug
      let counter = 1
      let newSlug = `${data.slug}-${counter}`

      while (await prisma.legalpages.findFirst({
        where: {
          slug: newSlug,
          id: { not: id }
        }
      })) {
        counter++
        newSlug = `${data.slug}-${counter}`
      }

      data.slug = newSlug
    }
  }

  // Transform data to database format
  const dbData = transformToDbFields.legalPage(data)

  const legalPage = await prisma.legalpages.update({
    where: { id },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  const transformedLegalPage = transformFromDbFields.legalPage(legalPage)
  return successResponse(transformedLegalPage, 'Legal page updated successfully')
})

// PATCH /api/admin/legal-pages/[id] - Partial update (e.g., toggle active status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if legal page exists
  const existingPage = await prisma.legalpages.findUnique({
    where: { id },
  })

  if (!existingPage) {
    throw new ApiError('Legal page not found', 404)
  }

  // Transform data to database format
  const dbData = transformToDbFields.legalPage(body)

  const updatedLegalPage = await prisma.legalpages.update({
    where: { id },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  const transformedLegalPage = transformFromDbFields.legalPage(updatedLegalPage)
  return successResponse(transformedLegalPage, 'Legal page updated successfully')
})

// DELETE /api/admin/legal-pages/[id] - Delete a legal page
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if legal page exists
  const existingPage = await prisma.legalpages.findUnique({
    where: { id },
  })

  if (!existingPage) {
    throw new ApiError('Legal page not found', 404)
  }

  // First delete all associated sections
  await prisma.legalpagesSection.deleteMany({
    where: { legalpageid: id }
  })

  // Then delete the legal page
  await prisma.legalpages.delete({
    where: { id }
  })

  return successResponse(null, 'Legal page deleted successfully')
})
