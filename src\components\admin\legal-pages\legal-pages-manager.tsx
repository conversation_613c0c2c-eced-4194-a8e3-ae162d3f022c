'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ScaleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { CheckCircleIcon as CheckCircleIconSolid } from '@heroicons/react/24/solid'
import { LegalPageModal } from './legal-page-modal'
import { LegalPageAvatar } from './legal-page-avatar'

interface LegalPage {
  id: string
  title: string
  slug: string
  content: string
  metaDescription?: string
  isActive: boolean
  displayOrder: number
  lastModified: string
  modifiedBy?: string
  createdAt: string
  updatedAt?: string
  sections?: Array<{
    id: string
    title: string
    content: string
    iconClass?: string
    displayOrder: number
    isActive: boolean
  }>
}

interface LegalPagesManagerProps {
  config: {
    title: string
    description: string
    apiEndpoint: string
    permissions: {
      create: boolean
      read: boolean
      update: boolean
      delete: boolean
      bulkActions: boolean
    }
    fields: Array<{
      key: string
      label: string
      type: string
      required?: boolean
      searchable?: boolean
      sortable?: boolean
      filterable?: boolean
      options?: Array<{ value: string; label: string }>
    }>
    actions: Array<{
      key: string
      label: string
      icon: string
      variant: 'primary' | 'secondary' | 'danger'
      permission?: string
    }>
    bulkActions: Array<{
      key: string
      label: string
      icon: string
      variant: 'primary' | 'secondary' | 'danger'
      permission?: string
    }>
    formLayout: {
      type: 'compact' | 'full'
      columns: number
      sections: Array<{
        title: string
        fields: string[]
      }>
    }
  }
}

type ViewMode = 'list' | 'grid' | 'cards'
type DensityMode = 'compact' | 'comfortable'

export function LegalPagesManager({ config }: LegalPagesManagerProps) {
  const [legalPages, setLegalPages] = useState<LegalPage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLegalPages, setSelectedLegalPages] = useState<string[]>([])
  const [showModal, setShowModal] = useState(false)
  const [editingLegalPage, setEditingLegalPage] = useState<LegalPage | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [density, setDensity] = useState<DensityMode>('comfortable')
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    'title', 'slug', 'isActive', 'lastModified', 'actions'
  ])

  const itemsPerPage = density === 'compact' ? 15 : 10

  // Fetch legal pages
  const fetchLegalPages = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        search: searchTerm,
        sortBy,
        sortOrder
      })

      const response = await fetch(`${config.apiEndpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch legal pages')
      
      const data = await response.json()
      setLegalPages(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / itemsPerPage))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch legal pages')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchLegalPages()
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortOrder])

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage !== 1) {
        setCurrentPage(1)
      } else {
        fetchLegalPages()
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Filtered and sorted legal pages
  const filteredLegalPages = useMemo(() => {
    return legalPages.filter(legalPage => {
      if (!searchTerm) return true
      
      const searchLower = searchTerm.toLowerCase()
      return (
        legalPage.title.toLowerCase().includes(searchLower) ||
        legalPage.slug.toLowerCase().includes(searchLower) ||
        legalPage.content.toLowerCase().includes(searchLower) ||
        (legalPage.metaDescription && legalPage.metaDescription.toLowerCase().includes(searchLower))
      )
    })
  }, [legalPages, searchTerm])

  // Handle actions
  const handleCreate = () => {
    setEditingLegalPage(null)
    setShowModal(true)
  }

  const handleEdit = (legalPage: LegalPage) => {
    setEditingLegalPage(legalPage)
    setShowModal(true)
  }

  const handleView = (legalPage: LegalPage) => {
    // Open in new tab or modal for viewing
    window.open(`/legal/${legalPage.slug}`, '_blank')
  }

  const handleDelete = async (legalPageId: string) => {
    if (!confirm('Are you sure you want to delete this legal page?')) return

    try {
      const response = await fetch(`${config.apiEndpoint}/${legalPageId}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) throw new Error('Failed to delete legal page')
      
      await fetchLegalPages()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete legal page')
    }
  }

  const handleToggleActive = async (legalPageId: string) => {
    try {
      const legalPage = legalPages.find(p => p.id === legalPageId)
      if (!legalPage) return

      const response = await fetch(`${config.apiEndpoint}/${legalPageId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !legalPage.isActive })
      })
      
      if (!response.ok) throw new Error('Failed to update legal page status')
      
      await fetchLegalPages()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update legal page status')
    }
  }

  const handleBulkAction = async (action: string) => {
    if (selectedLegalPages.length === 0) return

    try {
      let endpoint = config.apiEndpoint
      let method = 'PUT'
      let body: any = { ids: selectedLegalPages }

      switch (action) {
        case 'activate':
          body.data = { isActive: true }
          break
        case 'deactivate':
          body.data = { isActive: false }
          break
        case 'delete':
          method = 'DELETE'
          break
        default:
          return
      }

      const response = await fetch(endpoint, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      })

      if (!response.ok) throw new Error(`Failed to ${action} legal pages`)
      
      setSelectedLegalPages([])
      await fetchLegalPages()
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to ${action} legal pages`)
    }
  }

  const handleSelectAll = () => {
    if (selectedLegalPages.length === filteredLegalPages.length) {
      setSelectedLegalPages([])
    } else {
      setSelectedLegalPages(filteredLegalPages.map(p => p.id))
    }
  }

  const handleSelectLegalPage = (legalPageId: string) => {
    setSelectedLegalPages(prev => 
      prev.includes(legalPageId)
        ? prev.filter(id => id !== legalPageId)
        : [...prev, legalPageId]
    )
  }

  const getTypeIcon = (title: string) => {
    const titleLower = title.toLowerCase()
    if (titleLower.includes('privacy')) return ShieldCheckIcon
    if (titleLower.includes('terms')) return DocumentTextIcon
    if (titleLower.includes('policy')) return ScaleIcon
    return ExclamationTriangleIcon
  }

  if (loading && legalPages.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="mb-8">
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              {config.title}
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              {config.description}
            </p>
          </div>
          <div className="mt-4 flex md:mt-0 md:ml-4">
            {config.permissions.create && (
              <button
                onClick={handleCreate}
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
                Add Legal Page
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search legal pages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* View Controls */}
            <div className="flex items-center space-x-4">
              {/* View Mode */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                  title="List View"
                >
                  <ListBulletIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                  title="Grid View"
                >
                  <Squares2X2Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('cards')}
                  className={`p-2 rounded-md ${viewMode === 'cards' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'}`}
                  title="Card View"
                >
                  <RectangleStackIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Density */}
              <div className="relative">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                  {density === 'compact' ? 'Compact' : 'Comfortable'}
                  <ChevronDownIcon className="h-4 w-4 ml-2" />
                </button>

                {showFilters && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                    <div className="py-1">
                      <button
                        onClick={() => {
                          setDensity('comfortable')
                          setShowFilters(false)
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${density === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
                      >
                        Comfortable
                      </button>
                      <button
                        onClick={() => {
                          setDensity('compact')
                          setShowFilters(false)
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${density === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
                      >
                        Compact
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Column Selector */}
              {viewMode === 'list' && (
                <div className="relative">
                  <button
                    onClick={() => setShowColumnSelector(!showColumnSelector)}
                    className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Columns
                    <ChevronDownIcon className="h-4 w-4 ml-2" />
                  </button>

                  {showColumnSelector && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                      <div className="py-1">
                        {config.fields.map((field) => (
                          <label key={field.key} className="flex items-center px-4 py-2 hover:bg-gray-100">
                            <input
                              type="checkbox"
                              checked={visibleColumns.includes(field.key)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setVisibleColumns([...visibleColumns, field.key])
                                } else {
                                  setVisibleColumns(visibleColumns.filter(col => col !== field.key))
                                }
                              }}
                              className="mr-2"
                            />
                            <span className="text-sm text-gray-700">{field.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedLegalPages.length > 0 && config.permissions.bulkActions && (
            <div className="mt-4 flex items-center justify-between bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <span className="text-sm font-medium text-blue-900">
                  {selectedLegalPages.length} legal page{selectedLegalPages.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {config.bulkActions.map((action) => (
                  <button
                    key={action.key}
                    onClick={() => handleBulkAction(action.key)}
                    className={`px-3 py-1 rounded text-sm font-medium ${
                      action.variant === 'danger'
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : action.variant === 'primary'
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-600 text-white hover:bg-gray-700'
                    }`}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {viewMode === 'list' && (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {config.permissions.bulkActions && (
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedLegalPages.length === filteredLegalPages.length && filteredLegalPages.length > 0}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                  )}
                  {visibleColumns.includes('title') && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Title
                    </th>
                  )}
                  {visibleColumns.includes('slug') && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Slug
                    </th>
                  )}
                  {visibleColumns.includes('isActive') && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  )}
                  {visibleColumns.includes('lastModified') && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Last Modified
                    </th>
                  )}
                  {visibleColumns.includes('actions') && (
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredLegalPages.map((legalPage) => (
                  <tr key={legalPage.id} className="hover:bg-gray-50">
                    {config.permissions.bulkActions && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedLegalPages.includes(legalPage.id)}
                          onChange={() => handleSelectLegalPage(legalPage.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                    )}
                    {visibleColumns.includes('title') && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <LegalPageAvatar
                            title={legalPage.title}
                            size="sm"
                          />
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {legalPage.title}
                            </div>
                            {legalPage.metaDescription && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {legalPage.metaDescription}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    )}
                    {visibleColumns.includes('slug') && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900 font-mono">
                          /{legalPage.slug}
                        </span>
                      </td>
                    )}
                    {visibleColumns.includes('isActive') && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          legalPage.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {legalPage.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                    )}
                    {visibleColumns.includes('lastModified') && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(legalPage.lastModified).toLocaleDateString()}
                      </td>
                    )}
                    {visibleColumns.includes('actions') && (
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleView(legalPage)}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="View"
                          >
                            <EyeIcon className="h-5 w-5" />
                          </button>
                          {config.permissions.update && (
                            <button
                              onClick={() => handleEdit(legalPage)}
                              className="text-gray-400 hover:text-blue-600 transition-colors"
                              title="Edit"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                          )}
                          <button
                            onClick={() => handleToggleActive(legalPage.id)}
                            className={`transition-colors ${
                              legalPage.isActive
                                ? 'text-green-600 hover:text-green-700'
                                : 'text-gray-400 hover:text-green-600'
                            }`}
                            title={legalPage.isActive ? 'Deactivate' : 'Activate'}
                          >
                            {legalPage.isActive ? (
                              <CheckCircleIconSolid className="h-5 w-5" />
                            ) : (
                              <CheckCircleIcon className="h-5 w-5" />
                            )}
                          </button>
                          {config.permissions.delete && (
                            <button
                              onClick={() => handleDelete(legalPage.id)}
                              className="text-gray-400 hover:text-red-600 transition-colors"
                              title="Delete"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          )}
                        </div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Grid View */}
      {viewMode === 'grid' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredLegalPages.map((legalPage) => (
            <motion.div
              key={legalPage.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <LegalPageAvatar
                    title={legalPage.title}
                    size="md"
                  />
                  {config.permissions.bulkActions && (
                    <input
                      type="checkbox"
                      checked={selectedLegalPages.includes(legalPage.id)}
                      onChange={() => handleSelectLegalPage(legalPage.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  )}
                </div>

                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {legalPage.title}
                </h3>

                <p className="text-sm text-gray-500 mb-4 line-clamp-2">
                  {legalPage.metaDescription || 'No description available'}
                </p>

                <div className="flex items-center justify-between">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    legalPage.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {legalPage.isActive ? 'Active' : 'Inactive'}
                  </span>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleView(legalPage)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="View"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    {config.permissions.update && (
                      <button
                        onClick={() => handleEdit(legalPage)}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="Edit"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    )}
                    {config.permissions.delete && (
                      <button
                        onClick={() => handleDelete(legalPage.id)}
                        className="text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Card View */}
      {viewMode === 'cards' && (
        <div className="space-y-4">
          {filteredLegalPages.map((legalPage) => (
            <motion.div
              key={legalPage.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <LegalPageAvatar
                      title={legalPage.title}
                      size="lg"
                    />
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        {legalPage.title}
                      </h3>
                      <p className="text-sm text-gray-500 mb-2">
                        /{legalPage.slug}
                      </p>
                      <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                        {legalPage.metaDescription || 'No description available'}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>Modified: {new Date(legalPage.lastModified).toLocaleDateString()}</span>
                        {legalPage.modifiedBy && (
                          <span>By: {legalPage.modifiedBy}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 ml-4">
                    {config.permissions.bulkActions && (
                      <input
                        type="checkbox"
                        checked={selectedLegalPages.includes(legalPage.id)}
                        onChange={() => handleSelectLegalPage(legalPage.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}

                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      legalPage.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {legalPage.isActive ? 'Active' : 'Inactive'}
                    </span>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleView(legalPage)}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="View"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </button>
                      {config.permissions.update && (
                        <button
                          onClick={() => handleEdit(legalPage)}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                      )}
                      <button
                        onClick={() => handleToggleActive(legalPage.id)}
                        className={`transition-colors ${
                          legalPage.isActive
                            ? 'text-green-600 hover:text-green-700'
                            : 'text-gray-400 hover:text-green-600'
                        }`}
                        title={legalPage.isActive ? 'Deactivate' : 'Activate'}
                      >
                        {legalPage.isActive ? (
                          <CheckCircleIconSolid className="h-5 w-5" />
                        ) : (
                          <CheckCircleIcon className="h-5 w-5" />
                        )}
                      </button>
                      {config.permissions.delete && (
                        <button
                          onClick={() => handleDelete(legalPage.id)}
                          className="text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Modal */}
      <AnimatePresence>
        {showModal && (
          <LegalPageModal
            isOpen={showModal}
            onClose={() => {
              setShowModal(false)
              setEditingLegalPage(null)
            }}
            onSave={fetchLegalPages}
            legalPage={editingLegalPage}
            config={config}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
