'use client'

import React from 'react'
import { 
  DocumentTextIcon,
  ShieldCheckIcon,
  ScaleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  UserIcon,
  CogIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

interface LegalPageAvatarProps {
  title: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full-height'
  className?: string
}

export function LegalPageAvatar({ title, size = 'md', className = '' }: LegalPageAvatarProps) {
  // Get icon based on legal page title/type
  const getIcon = (title: string) => {
    const titleLower = title.toLowerCase()
    
    if (titleLower.includes('privacy')) return ShieldCheckIcon
    if (titleLower.includes('terms') || titleLower.includes('service')) return DocumentTextIcon
    if (titleLower.includes('policy') || titleLower.includes('policies')) return ScaleIcon
    if (titleLower.includes('cookie') || titleLower.includes('cookies')) return CogIcon
    if (titleLower.includes('disclaimer') || titleLower.includes('liability')) return ExclamationTriangleIcon
    if (titleLower.includes('about') || titleLower.includes('company')) return InformationCircleIcon
    if (titleLower.includes('contact') || titleLower.includes('support')) return UserIcon
    if (titleLower.includes('accessibility') || titleLower.includes('compliance')) return GlobeAltIcon
    
    // Default icon
    return DocumentTextIcon
  }

  // Get color based on legal page type
  const getColor = (title: string) => {
    const titleLower = title.toLowerCase()
    
    if (titleLower.includes('privacy')) return 'bg-blue-100 text-blue-600'
    if (titleLower.includes('terms') || titleLower.includes('service')) return 'bg-green-100 text-green-600'
    if (titleLower.includes('policy') || titleLower.includes('policies')) return 'bg-purple-100 text-purple-600'
    if (titleLower.includes('cookie') || titleLower.includes('cookies')) return 'bg-orange-100 text-orange-600'
    if (titleLower.includes('disclaimer') || titleLower.includes('liability')) return 'bg-red-100 text-red-600'
    if (titleLower.includes('about') || titleLower.includes('company')) return 'bg-indigo-100 text-indigo-600'
    if (titleLower.includes('contact') || titleLower.includes('support')) return 'bg-teal-100 text-teal-600'
    if (titleLower.includes('accessibility') || titleLower.includes('compliance')) return 'bg-cyan-100 text-cyan-600'
    
    // Default color based on first letter
    const firstLetter = title.charAt(0).toUpperCase()
    const colors = [
      'bg-gray-100 text-gray-600',
      'bg-slate-100 text-slate-600',
      'bg-zinc-100 text-zinc-600',
      'bg-neutral-100 text-neutral-600',
      'bg-stone-100 text-stone-600'
    ]
    
    return colors[firstLetter.charCodeAt(0) % colors.length]
  }

  // Get size classes
  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'xs':
        return 'h-6 w-6'
      case 'sm':
        return 'h-8 w-8'
      case 'md':
        return 'h-10 w-10'
      case 'lg':
        return 'h-12 w-12'
      case 'xl':
        return 'h-16 w-16'
      case 'full-height':
        return 'h-full w-12'
      default:
        return 'h-10 w-10'
    }
  }

  // Get icon size classes
  const getIconSizeClasses = (size: string) => {
    switch (size) {
      case 'xs':
        return 'h-3 w-3'
      case 'sm':
        return 'h-4 w-4'
      case 'md':
        return 'h-5 w-5'
      case 'lg':
        return 'h-6 w-6'
      case 'xl':
        return 'h-8 w-8'
      case 'full-height':
        return 'h-6 w-6'
      default:
        return 'h-5 w-5'
    }
  }

  const Icon = getIcon(title)
  const colorClasses = getColor(title)
  const sizeClasses = getSizeClasses(size)
  const iconSizeClasses = getIconSizeClasses(size)

  return (
    <div className={`
      ${sizeClasses}
      ${colorClasses}
      rounded-lg
      flex
      items-center
      justify-center
      flex-shrink-0
      ${className}
    `}>
      <Icon className={iconSizeClasses} />
    </div>
  )
}
